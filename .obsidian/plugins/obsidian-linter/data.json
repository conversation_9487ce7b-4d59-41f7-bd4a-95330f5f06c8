{"ruleConfigs": {"add-blank-line-after-yaml": {"enabled": false}, "dedupe-yaml-array-values": {"enabled": false, "dedupe-alias-key": true, "dedupe-tag-key": true, "dedupe-array-keys": true, "ignore-keys": ""}, "escape-yaml-special-characters": {"enabled": false, "try-to-escape-single-line-arrays": false}, "force-yaml-escape": {"enabled": false, "force-yaml-escape-keys": ""}, "format-tags-in-yaml": {"enabled": false}, "format-yaml-array": {"enabled": false, "alias-key": true, "tag-key": true, "default-array-style": "single-line", "default-array-keys": true, "force-single-line-array-style": "", "force-multi-line-array-style": ""}, "insert-yaml-attributes": {"enabled": false, "text-to-insert": "aliases: \ntags: "}, "move-tags-to-yaml": {"enabled": false, "how-to-handle-existing-tags": "Nothing", "tags-to-ignore": ""}, "remove-yaml-keys": {"enabled": false, "yaml-keys-to-remove": ""}, "sort-yaml-array-values": {"enabled": false, "sort-alias-key": true, "sort-tag-key": true, "sort-array-keys": true, "ignore-keys": "", "sort-order": "Ascending Alphabetical"}, "yaml-key-sort": {"enabled": false, "yaml-key-priority-sort-order": "", "priority-keys-at-start-of-yaml": true, "yaml-sort-order-for-other-keys": "None"}, "yaml-timestamp": {"enabled": false, "date-created": true, "date-created-key": "date created", "date-created-source-of-truth": "file system", "date-modified": true, "date-modified-key": "date modified", "date-modified-source-of-truth": "file system", "format": "dddd, MMMM Do YYYY, h:mm:ss a", "convert-to-utc": false, "update-on-file-contents-updated": "never"}, "yaml-title": {"enabled": false, "title-key": "title", "mode": "first-h1-or-filename-if-h1-missing"}, "yaml-title-alias": {"enabled": false, "preserve-existing-alias-section-style": true, "keep-alias-that-matches-the-filename": false, "use-yaml-key-to-keep-track-of-old-filename-or-heading": true, "alias-helper-key": "linter-yaml-title-alias"}, "capitalize-headings": {"enabled": false, "style": "Title Case", "ignore-case-words": true, "ignore-words": "macOS, iOS, iPhone, iPad, JavaScript, TypeScript, AppleScript, I", "lowercase-words": "a, an, the, aboard, about, abt., above, abreast, absent, across, after, against, along, aloft, alongside, amid, amidst, mid, midst, among, amongst, anti, apropos, around, round, as, aslant, astride, at, atop, ontop, bar, barring, before, B4, behind, below, beneath, neath, beside, besides, between, 'tween, beyond, but, by, chez, circa, c., ca., come, concerning, contra, counting, cum, despite, spite, down, during, effective, ere, except, excepting, excluding, failing, following, for, from, in, including, inside, into, less, like, minus, modulo, mod, near, nearer, nearest, next, notwithstanding, of, o', off, offshore, on, onto, opposite, out, outside, over, o'er, pace, past, pending, per, plus, post, pre, pro, qua, re, regarding, respecting, sans, save, saving, short, since, sub, than, through, thru, throughout, thruout, till, times, to, t', touching, toward, towards, under, underneath, unlike, until, unto, up, upon, versus, vs., v., via, vice, vis-à-vis, wanting, with, w/, w., c̄, within, w/i, without, 'thout, w/o, abroad, adrift, aft, afterward, afterwards, ahead, apart, ashore, aside, away, back, backward, backwards, beforehand, downhill, downstage, downstairs, downstream, downward, downwards, downwind, east, eastward, eastwards, forth, forward, forwards, heavenward, heavenwards, hence, henceforth, here, hereby, herein, hereof, hereto, herewith, home, homeward, homewards, indoors, inward, inwards, leftward, leftwards, north, northeast, northward, northwards, northwest, now, onward, onwards, outdoors, outward, outwards, overboard, overhead, overland, overseas, rightward, rightwards, seaward, seawards, skywards, skyward, south, southeast, southwards, southward, southwest, then, thence, thenceforth, there, thereby, therein, thereof, thereto, therewith, together, underfoot, underground, uphill, upstage, upstairs, upstream, upward, upwards, upwind, west, westward, westwards, when, whence, where, whereby, wherein, whereto, wherewith, although, because, considering, given, granted, if, lest, once, provided, providing, seeing, so, supposing, though, unless, whenever, whereas, wherever, while, whilst, ago, according to, as regards, counter to, instead of, owing to, pertaining to, at the behest of, at the expense of, at the hands of, at risk of, at the risk of, at variance with, by dint of, by means of, by virtue of, by way of, for the sake of, for sake of, for lack of, for want of, from want of, in accordance with, in addition to, in case of, in charge of, in compliance with, in conformity with, in contact with, in exchange for, in favor of, in front of, in lieu of, in light of, in the light of, in line with, in place of, in point of, in quest of, in relation to, in regard to, with regard to, in respect to, with respect to, in return for, in search of, in step with, in touch with, in terms of, in the name of, in view of, on account of, on behalf of, on grounds of, on the grounds of, on the part of, on top of, with a view to, with the exception of, à la, a la, as soon as, as well as, close to, due to, far from, in case, other than, prior to, pursuant to, regardless of, subsequent to, as long as, as much as, as far as, by the time, in as much as, inasmuch, in order to, in order that, even, provide that, if only, whether, whose, whoever, why, how, or not, whatever, what, both, and, or, not only, but also, either, neither, nor, just, rather, no sooner, such, that, yet, is, it"}, "file-name-heading": {"enabled": false}, "header-increment": {"enabled": false, "start-at-h2": false}, "headings-start-line": {"enabled": false}, "remove-trailing-punctuation-in-heading": {"enabled": false, "punctuation-to-remove": ".,;:!。，；：！"}, "footnote-after-punctuation": {"enabled": false}, "move-footnotes-to-the-bottom": {"enabled": false}, "re-index-footnotes": {"enabled": false}, "auto-correct-common-misspellings": {"enabled": false, "ignore-words": "", "skip-words-with-multiple-capitals": false, "extra-auto-correct-files": []}, "blockquote-style": {"enabled": false, "style": "space"}, "convert-bullet-list-markers": {"enabled": false}, "default-language-for-code-fences": {"enabled": false, "default-language": ""}, "emphasis-style": {"enabled": false, "style": "consistent"}, "no-bare-urls": {"enabled": false, "no-bare-uris": false}, "ordered-list-style": {"enabled": false, "number-style": "ascending", "list-end-style": "."}, "proper-ellipsis": {"enabled": false}, "quote-style": {"enabled": false, "single-quote-enabled": true, "single-quote-style": "''", "double-quote-enabled": true, "double-quote-style": "\"\""}, "remove-consecutive-list-markers": {"enabled": false}, "remove-empty-list-markers": {"enabled": false}, "remove-hyphenated-line-breaks": {"enabled": false}, "remove-multiple-spaces": {"enabled": false}, "strong-style": {"enabled": false, "style": "consistent"}, "two-spaces-between-lines-with-content": {"enabled": false, "line-break-indicator": "  "}, "unordered-list-style": {"enabled": false, "list-style": "consistent"}, "compact-yaml": {"enabled": false, "inner-new-lines": false}, "consecutive-blank-lines": {"enabled": false}, "convert-spaces-to-tabs": {"enabled": false, "tabsize": 4}, "empty-line-around-blockquotes": {"enabled": false}, "empty-line-around-code-fences": {"enabled": false}, "empty-line-around-horizontal-rules": {"enabled": false}, "empty-line-around-math-blocks": {"enabled": false}, "empty-line-around-tables": {"enabled": false}, "heading-blank-lines": {"enabled": false, "bottom": true, "empty-line-after-yaml": true}, "line-break-at-document-end": {"enabled": false}, "move-math-block-indicators-to-their-own-line": {"enabled": false}, "paragraph-blank-lines": {"enabled": false}, "remove-empty-lines-between-list-markers-and-checklists": {"enabled": false}, "remove-link-spacing": {"enabled": false}, "remove-space-around-characters": {"enabled": false, "include-fullwidth-forms": true, "include-cjk-symbols-and-punctuation": true, "include-dashes": true, "other-symbols": ""}, "remove-space-before-or-after-characters": {"enabled": false, "characters-to-remove-space-before": ",!?;:).’”]", "characters-to-remove-space-after": "¿¡‘“(["}, "space-after-list-markers": {"enabled": false}, "space-between-chinese-japanese-or-korean-and-english-or-numbers": {"enabled": false, "english-symbols-punctuation-before": "-+;:'\"°%$)]", "english-symbols-punctuation-after": "-+'\"([¥$"}, "trailing-spaces": {"enabled": false, "twp-space-line-break": false}, "add-blockquote-indentation-on-paste": {"enabled": false}, "prevent-double-checklist-indicator-on-paste": {"enabled": false}, "prevent-double-list-item-indicator-on-paste": {"enabled": false}, "proper-ellipsis-on-paste": {"enabled": false}, "remove-hyphens-on-paste": {"enabled": false}, "remove-leading-or-trailing-whitespace-on-paste": {"enabled": false}, "remove-leftover-footnotes-from-quote-on-paste": {"enabled": false}, "remove-multiple-blank-lines-on-paste": {"enabled": false}}, "lintOnSave": false, "recordLintOnSaveLogs": false, "displayChanged": true, "lintOnFileChange": false, "displayLintOnFileChangeNotice": false, "settingsConvertedToConfigKeyValues": true, "foldersToIgnore": [], "filesToIgnore": [], "linterLocale": "system-default", "logLevel": "ERROR", "lintCommands": [], "customRegexes": [], "commonStyles": {"aliasArrayStyle": "single-line", "tagArrayStyle": "single-line", "minimumNumberOfDollarSignsToBeAMathBlock": 2, "escapeCharacter": "\"", "removeUnnecessaryEscapeCharsForMultiLineArrays": false}}