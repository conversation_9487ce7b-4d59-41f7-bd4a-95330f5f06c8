{"path": "cruca-documentation/2-Areas/0-Monthly-Tasks&Reports/April/CRCC Administrators report/C********-Complete-the-Safe-Church-Audit-in-Protecht.pdf", "text": "Instructions This document is uncontrolled when printed. C/******** Login to Protecht – Congregations & Presbyteries 1 of 6 Effective date 22.08.2024 Complete the Safe Church Audit in Protecht C/******** Overview In the past, we relied on an email process to manage the Safe Church Audit. Presbyteries would email requests to congregations and congregations would respond via email. Now that congregations and presbyteries have access to Protecht, we can use better tools to do the job more quickly and easily. The new process involves the Synod Risk and Compliance Team assigning questions to one of your congregation’s Protecht account-holders. That person then signs in and answers a series of questions, adding documents to show that you have various processes in place. Follow the steps below to help you complete the Safe Church Audit online in Protecht. Scope These instructions apply only to people assigned a Protecht login in congregations and presbyteries. Know your credentials Username and password for Protecht. This would have been organised as part of the round of rollout meetings mid-2024. Your minister or church council leaders know who your account holders are. If you're not sure, check your details with a member of the Risk and Compliance team <NAME_EMAIL>. Find the right records and documents Before you do anything else, locate the records and documents below. This will make the process much faster and easier once you log in. Preparation Notes Minutes of the church council meeting where your congregation adopted the Safe Church Policy. Each year, church councils adopt this synod policy. The minutes of that meeting, or a copy of the template serves as proof that this has been done. A list of any child-related activities that your congregation provides. Include Baptisms. Register of Workers for your congregation. This is a list of paid and/or volunteer workers who work with children, along with each person’s blue card information, training details and pre-appointment screening details. Statements of Personal Commitment for all: • Workers • Volunteers • Ministry agents (Statement of Commitment) Each year, anyone who works with children, signs one of these statements as a reminder and renewal of their commitment to safe practices involving children and vulnerable people. Instructions: Complete the Safe Church Audit in Protecht This document is uncontrolled when printed. C/******** Login to Protecht – Congregations & Presbyteries 2 of 6 Effective date 22.08.2024 Blue Card register You need to keep a record of the Blue Cards for people in your congregation. You may be using: • The Blue Card Services Portal • Another method of your own A risk assessment for an activity that your congregation has run. A risk assessment helps to identify things that could cause harm to leaders, participants or property and plan to reduce the likelihood of these things happening. Breach register A register where you can record instances where the Safe Church Policy has not been followed. Login to Protecht If this is your first time logging in to Protecht, you may want to review how to login to Protecht for congregations and presbyteries. 1. Open Protecht. 2. Enter your username and password and click Login. Open Compliance Attestations When you’re logged in, you’ll see the Protecht home page. 1. In the top menu, choose Compliance Attestations > My Compliance Entry. Figure 1: Open My Compliance Entry You should see a list of questions that have been assigned to you. Instructions: Complete the Safe Church Audit in Protecht This document is uncontrolled when printed. C/******** Login to Protecht – Congregations & Presbyteries 3 of 6 Effective date 22.08.2024 Figure 2: List of questions If you can’t see a list of questions on this page, it may be that the questions for your congregation have been assigned to someone else. Email <EMAIL> and let us know so we can reassign the questions to you. Respond to the questions 3. Click the first question. It will expand to show more detail. You can read through the description and the attachments on the left if you need. Figure 3: Question expanded to show detail 2. Once you’re ready to respond, click Yes, In Progress or No. 3. Leave a comment if you’d like to provide an explanation. Adding Evidence For some questions, you’ll need to upload a document to show that your congregation has met certain requirements. Question 1 is a good example. You’ll need to attach a copy of your church council minutes showing a motion to adopt the Safe Church Policy. Instructions: Complete the Safe Church Audit in Protecht This document is uncontrolled when printed. C/******** Login to Protecht – Congregations & Presbyteries 4 of 6 Effective date 22.08.2024 Method 1 – drag and drop 1. Open a window on your computer and navigate to a folder containing your minutes. 2. Position this window near the browser. 3. Drag the document on to the Evidence panel on the right. Figure 4: Using drag and drop to add evidence Method 2 – Select 1. Click Select in the Evidence panel. 2. Navigate to the relevant folder on your computer. 3. Click the file to upload. 4. Click Open. Instructions: Complete the Safe Church Audit in Protecht This document is uncontrolled when printed. C/******** Login to Protecht – Congregations & Presbyteries 5 of 6 Effective date 22.08.2024 Figure 5: Method 2: Using Select to open a window and choose a document. Figure 6: After the document has successfully uploaded. 5. Click Save. Your work will be recorded, and the next question will open. Instructions: Complete the Safe Church Audit in Protecht This document is uncontrolled when printed. C/******** Login to Protecht – Congregations & Presbyteries 6 of 6 Effective date 22.08.2024 Figure 7: Click Save Complete the rest of the questions Continue with the process until you have responded to all questions. At the end of the process, you should see something like this. Figure 8: All questions show a coloured response when you have responded to them. Revisions Document number C/******** Version Approval date Approved by Effective date Policy owner Policy contact 1.0 22.08.2024 Program Manager, Risk and Assurance 22.08.2024 General Manager – Risk & Safeguarding Program Manager, Risk and Assurance Next scheduled review 03.12.2024", "libVersion": "0.5.0", "langs": ""}