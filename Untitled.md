---
creation_date: 2025-06-13 02:56
modification_date: Friday 13th June 2025 02:56:04
type: daily
date: 2025-06-13
day_of_week: Friday
week: 2025-W24
month: 2025-06
tags: [daily, 2025-06, cruca]
mood: ""
energy_level: ""
weather: ""
location: ""
---

# 2025-06-13 - Friday

<< [[2025-06-12]] | [[2025-06-14]] >>

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- 

## Tasks for Today
#todo #tasks #outstanding 
### ✨ Tasks for Today

- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care, services -->
- 

## Hall Hire
<!-- Hall bookings, inquiries, maintenance, key management -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation, reports -->
- 

## Finance & Banking
<!-- Banking, donations, payments, reconciliation, invoices -->
- 

## Safe Church
<!-- Compliance, training, documentation, Blue Card updates -->
- 

## Communication
<!-- Emails, phone calls, website updates, social media -->
- 

## Facilities & Maintenance
<!-- Building maintenance, equipment, cleaning, security -->
- 

## Follow-ups
<!-- Items requiring follow-up action with specific people/organizations -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-06-13)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-06-13
```

## Tasks from This Note
```dataview
TASK
FROM this.file
WHERE !completed
```

## Overdue Tasks
```tasks
not done
due before 2025-06-13
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-06-13
due before 2025-06-20
```

## Church Administration Tasks
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Today's Events & Reminders
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  time as "Time",
  location as "Location",
  description as "Description"
FROM "Events&Meeting Dates" OR "2-Areas/0-Monthly-Tasks&Reports"
WHERE date = date(2025-06-13)
SORT time ASC
```

## Weekly Recurring Events (Today: Friday)
```dataview
TABLE WITHOUT ID
  file.link as "Activity",
  time as "Time",
  location as "Location",
  description as "Notes"
FROM "Events&Meeting Dates"
WHERE contains(tags, "recurring") AND contains(file.content, "Friday")
```

## Create New Note
- [[2025-06-13 Meeting|Create New Meeting]]
- [[2025-06-13 Task|Create New Task]]
- [[2025-06-13 Hall Hire|Create Hall Hire Booking]]
- [[2025-06-13 Finance|Create Finance Note]]

## Related
- [[Daily Notes TOC]]
- [[2025-06|Monthly Overview]]
- [[2025-W24|Weekly Overview]]
- [[Comprehensive Administrative Procedures Guide]]
- [[Home]]
