---
creation_date: 2025-04-09
modification_date: 2025-04-16
type: resource
aliases:
  - Resources
  - finance
  - procedures
tags:
  - para/resources
  - index
  - bank
  - admin
  - admin/documentation
  - finance
  - BBO
  - procedures
  - church
  - cruca
  - osko
  - payments
  - reconciliation
  - donations
source: <PERSON><PERSON>, Senior Account Executive, Industry Banking QLD
related:
  - Church Administration
  - Administration Overview
  - Church TOC
area: Church-Administration
bank: Commonwealth Bank
platform: Business Banking Online (BBO)
contact_person: <PERSON><PERSON>
contact_role: Senior Account Executive, Industry Banking QLD
---
# Bank Account and Finance Guides

## Overview
Collection of banking and finance procedures for church administration, including how to identify and track different types of payments.
## Identifying Osko Payments

Email from Marsha RE: identifying OSKO payments:

> Hi Jordan,
>
> Please see below regarding getting additional information on an OSKO deposit.
>
> How do I create a report to see the extra Osko payment details?
>
> The extra payment details available with Osko payments are visible in a report after 8pm on the second day after an Osko payment has been received. There may be delays in this information being visible in the reports when an Osko payment is received after 10pm on a week day, on a weekend and on a Australia-wide public holiday. To create report to add the new Osko fields follow these instructions:
>
> 1. Select Reports
> 2. In the custom export formats tab of the Transactions section, click the Add New button
> 3. Select your required fields for the export, including the new Osko rich data fields
> 4. In the Name field, enter a name for the new report extract
> 5. Select the additional export information required for your report, press Continue, then press Save.
> 6. To extract the new report, go to the Transaction history tab, select Download report, choose a date range and select the account.
> 7. In the File Format drop down menu, select your new report.
> 8. Press the Download report button to download and view your report. You can now use this report to view Osko payment details going forward.
>
> Regards,
> Marsha McIntyre
> Senior Account Executive
> Industry Banking QLD

## Banking Procedures

### Accessing Bank Statements
1. Log in to the Business Banking Online (BBO) portal
2. Navigate to the Accounts section
3. Select the appropriate account
4. Choose the date range for the statement
5. Download the statement in the desired format

### Reconciling Donations
1. Download the bank statement with Osko payment details
2. Match donations to donor records
3. Update the donation tracking spreadsheet
4. Note any unidentified donations for follow-up

### Handling Unidentified Payments
1. Check for any identifying information in the Osko payment details
2. Contact the bank for additional information if necessary
3. Announce unidentified donations during church service
4. Keep a record of unidentified donations for future matching

## Tasks
- [ ] Create custom report format for Osko payments
- [ ] Set up weekly donation reconciliation process
- [ ] Document procedure for handling unidentified payments
- [ ] Train volunteers on banking procedures

## Related
- [[Church Administration]]
- [[Administration Overview]]
- [[3-Resources]]
- [[Resources TOC]]


---
*Merged from duplicate:*
# Identifying Osko Payments
Email from Marsha RE: identifying OSKO payments:
Hi Jordan,

Please see below regarding getting additional information on an OSKO deposit.


How do I create a report to see the extra Osko payment details?
The extra payment details available with Osko payments are visible in a report after 8pm on the second day after an Osko payment has been received. There may be delays in this information being visible in the reports when an Osko payment is received after 10pm on a week day, on a weekend and on a Australia-wide public holiday. To create report to add the new Osko fields follow these instructions: 
1.	 Select Reports
2.	 In the custom export formats tab of the Transactions section, click the Add New button
3.	 Select your required fields for the export, including the new Osko rich data fields
4.	 In the Name field, enter a name for the new report extract
5.	 Select the additional export information required for your report, press Continue, then press Save. 
6.	 To extract the new report, go to the Transaction history tab, select Download report, choose a date range and select the account. 
7.	 In the File Format drop down menu, select your new report.
8.	 Press the Download report button to download and view your report. You can now use this report to view Osko payment details going forward. 

Regards,
Marsha McIntyre 
Senior Account Executive
Industry Banking QLD


##
- [ ] 


###
- 
