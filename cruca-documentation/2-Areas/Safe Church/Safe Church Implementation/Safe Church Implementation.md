---
creation_date: 2025-04-26
modification_date: 2025-04-26
type: project
status: active
priority: medium
deadline: 2025-05-26
project_owner: Jordan
project_client: cruca
completion_percentage: 0
estimated_hours: 25
tags: [para/projects, safe, church, protecht, policies, guidelines, legislation, compliance, audit]
related: []
area: administration
start_date: 2025-04-26
stakeholders: []
---

# Safe Church Implementation

## Overview
<!-- Brief description of the project -->
I have been entrusted to implement measures and plans for certain safety protocols for cruca. Using protecht, i will log these plans.
## Objectives
<!-- What are you trying to achieve? -->
- 

## Success Criteria
<!-- How will you know when the project is successful? -->
- 

## Tasks
<!-- List of tasks to complete -->
- [ ] update representatives and information with [[CRUCA-Church-Vault/Administration/Safe Church/Audit/workers and volunteers|workers and volunteers]] [[CRUCA-Church-Vault/Administration/Safe Church/Audit/Beachmere Ministry Areas 2024-2025.pdf|Beachmere Ministry Areas 2024-2025]] [[CRUCA-Church-Vault/Administration/Safe Church/Audit/CRUC Ministry Areas.pdf|CRUC Ministry Areas]]
- [ ] safety plans to be update with nominated people.
- [ ] provide training materials and keep record of volunteers who have completed/not completed them

## Timeline
- **Start Date**: 2025-04-26
- **Deadline**: 2025-05-26
- **Milestones**:
  - [ ] Initial Planning - 2025-05-03
  - [ ] Development - 2025-05-10
  - [ ] testing - 2025-05-17
  - [ ] Completion and feedback - 2025-05-26

## Resources
<!-- Links to relevant resources -->
- 

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.name, "safe church")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "safe church")
LIMIT 5
```

## Meeting Notes
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  participants as "Participants"
FROM #meeting
WHERE contains(related, "Safe Church Implementation") OR contains(file.name, "Safe Church Implementation")
SORT date DESC
```

## Progress Updates
<!-- Regular updates on project progress -->
### 2025-04-26 - Initial Setup
- Project created
- Initial planning started

## Create Related Notes
- [[Safe Church Implementation Meeting 2025-04-26|Create Meeting Note]]
- [[Safe Church Implementation Resource|Create Resource Note]]
- [[Safe Church Implementation Documentation|Create Documentation]]

## Related
- [[1-Projects]]
- [[Projects TOC]]
- [[administration Overview]]
