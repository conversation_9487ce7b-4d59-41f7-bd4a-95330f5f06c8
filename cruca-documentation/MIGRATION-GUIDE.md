# Migration Guide

This document provides instructions for migrating this folder to a new Obsidian vault.

## Steps to Create a New Vault

1. **Create a new folder** for your CRUCA vault outside of your current vault
   - Example: `~/Documents/CRUCA-Obsidian-Vault/`

2. **Copy this entire folder** (`CRUCA-Church-Vault`) to the new location
   - Make sure to include all subfolders and files

3. **Open Obsidian** and select "Open another vault"

4. Click "Open folder as vault" and select your new folder

5. **Install required plugins**:
   - Dataview
   - Calendar
   - Templater
   - Any other plugins you use regularly

6. **Configure settings**:
   - Enable "Folder note" core plugin if you use it
   - Set up your preferred appearance settings
   - Configure hotkeys as needed

## Post-Migration Tasks

1. **Verify links are working** by clicking through the main navigation pages:
   - Home
   - Projects TOC
   - Areas TOC
   - Resources TOC

2. **Update any broken links** using Obsidian's link suggestion feature

3. **Set up a new Git repository** (optional but recommended for backup):
   ```bash
   cd ~/Documents/CRUCA-Obsidian-Vault/
   git init
   git add .
   git commit -m "Initial commit of CRUCA vault"
   ```

4. **Connect to a new remote repository** (GitHub, GitLab, etc.) for backup:
   ```bash
   git remote add origin [your-new-repository-url]
   git push -u origin main
   ```

## Privacy Considerations

1. The `.gitignore` file is configured to exclude potentially sensitive information

2. Review any files containing passwords, credentials, or personal information before pushing to a remote repository

3. Consider using a private repository for additional security

## Troubleshooting

If you encounter issues with broken links or missing files:

1. Use Obsidian's "Check for broken links" feature in the Settings
2. Verify that all folders were copied correctly
3. Check that all plugins are installed and configured properly
