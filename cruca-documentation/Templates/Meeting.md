---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: meeting
date: <% tp.date.now("YYYY-MM-DD") %>
time: <% await tp.system.prompt("Meeting Time", tp.date.now("HH:mm")) %>
location: <% await tp.system.suggester(["Online", "Office", "Church", "Other"], ["Online", "Office", "Church", await tp.system.prompt("Location")], false, "Location") %>
participants: [<% await tp.system.prompt("Participants (comma-separated)", "") %>]
tags: [meeting]
project: <% await tp.system.suggester(["None", "Other"], ["", await tp.system.prompt("Related Project")], false, "Related Project") %>
area: <% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church", "None", "Other"], ["Software-Development", "Administration", "Personal", "Church", "", await tp.system.prompt("Area Name")], false, "Related Area") %>
status: <% await tp.system.suggester(["scheduled", "completed", "cancelled"], ["scheduled", "completed", "cancelled"], false, "Status") %>
---

# <% tp.file.title %>

## Details
- **Date**: <% tp.date.now("YYYY-MM-DD") %>
- **Time**: <% tp.date.now("HH:mm") %>
- **Location**: <% await tp.system.suggester(["Online", "Office", "Church"], ["Online", "Office", "Church"], false, "Location") %>
- **Participants**: <% await tp.system.prompt("Participants (comma-separated)", "") %>

## Agenda
-

## Discussion Points
-

## Decisions
-

## Action Items
- [ ]

## Related Projects
```dataview
LIST
FROM "1-Projects"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR project = "<% await tp.system.suggester(["", "Other"], ["", await tp.system.prompt("Filter Project")], false, "Filter Project") %>"
```

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(file.content, "[[<% tp.file.title %>]]") OR area = "<% await tp.system.suggester(["Software-Development", "Administration", "Personal", "Church"], ["Software-Development", "Administration", "Personal", "Church"], false, "Filter Area") %>"
```

## Quick Links
- [[<% tp.date.now("YYYY-MM-DD") %>|Daily Note]]
- [[Tasks]]
