---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: meeting
date: <% tp.date.now("YYYY-MM-DD") %>
time: <% await tp.system.prompt("Meeting Time", tp.date.now("HH:mm")) %>
end_time: <% await tp.system.prompt("End Time", "") %>
location: <% await tp.system.prompt("Location", "Online") %>
participants: [<% await tp.system.prompt("Participants (comma-separated)", "") %>]
tags: [meeting, <% await tp.system.prompt("Additional Tags (comma-separated)", "") %>]
related: [<% await tp.system.prompt("Related Notes (comma-separated)", "") %>]
project: <% await tp.system.prompt("Related Project (if applicable)", "") %>
area: <% await tp.system.prompt("Related Area (if applicable)", "") %>
status: <% await tp.system.prompt("Status", "scheduled", "scheduled, completed, cancelled") %>
---

# <% tp.file.title %>

## Details
- **Date**: <% tp.date.now("YYYY-MM-DD") %>
- **Time**: <% await tp.system.prompt("Meeting Time", tp.date.now("HH:mm")) %> - <% await tp.system.prompt("End Time", "") %>
- **Location**: <% await tp.system.prompt("Location", "Online") %>
- **Participants**: <% await tp.system.prompt("Participants (comma-separated)", "") %>
- **Project**: [[<% await tp.system.prompt("Related Project (if applicable)", "") %>]]
- **Area**: [[<% await tp.system.prompt("Related Area (if applicable)", "") %>]]

## Agenda
-

## Discussion Points
-

## Decisions
-

## Action Items
- [ ] <% await tp.system.prompt("Action Item 1", "") %> - Assigned to: <% await tp.system.prompt("Assigned To", "") %>, Due: <% await tp.system.prompt("Due Date", tp.date.now("YYYY-MM-DD", 7)) %>
- [ ] <% await tp.system.prompt("Action Item 2", "") %> - Assigned to: <% await tp.system.prompt("Assigned To", "") %>, Due: <% await tp.system.prompt("Due Date", tp.date.now("YYYY-MM-DD", 7)) %>

## Notes
-

## Follow-up
- Next meeting: <% await tp.system.prompt("Next Meeting Date", tp.date.now("YYYY-MM-DD", 7)) %>

## Related Projects
```dataview
LIST
FROM "1-Projects"
WHERE project = "<% await tp.system.prompt("Related Project (if applicable)", "") %>" OR contains(file.content, "[[<% tp.file.title %>]]")
```

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE area = "<% await tp.system.prompt("Related Area (if applicable)", "") %>" OR contains(file.content, "[[<% tp.file.title %>]]")
```

## Related
- [[Daily/<% tp.date.now("YYYY-MM-DD") %>|Daily Note]]
- [[<% await tp.system.prompt("Related Project (if applicable)", "") %>]]
- [[<% await tp.system.prompt("Related Area (if applicable)", "") %>]]
- [[Tasks]]
