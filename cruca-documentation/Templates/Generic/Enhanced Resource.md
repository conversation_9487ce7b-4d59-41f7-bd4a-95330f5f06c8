---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: resource
source: <% await tp.system.prompt("Source", "Personal research") %>
tags: [para/resources, <% await tp.system.prompt("Additional Tags (comma-separated)", "guide") %>]
related: []
area: <% await tp.system.prompt("Related Area", "Software-Development") %>
difficulty: <% await tp.system.prompt("Difficulty", "medium", "easy, medium, hard") %>
keywords: [<% await tp.system.prompt("Keywords (comma-separated)", "guide, reference") %>]
last_used: <% tp.date.now("YYYY-MM-DD") %>
url: <% await tp.system.prompt("URL (if applicable)", "") %>
author: <% await tp.system.prompt("Author (if applicable)", "") %>
---

# <% tp.file.title %>

## Overview
<!-- Brief description of this resource -->

## Key Points
<!-- Main takeaways or important information -->
- 

## Details
<!-- Detailed information -->

## Examples
<!-- Examples or code snippets if applicable -->
```
// Code example
```

## Use Cases
<!-- When and how to use this resource -->
- 

## Related Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  status as "Status",
  priority as "Priority"
FROM "1-Projects"
WHERE contains(related, "<% tp.file.title %>") OR contains(file.content, "[[<% tp.file.title %>]]")
SORT priority ASC
```

## Related Areas
```dataview
TABLE WITHOUT ID
  file.link as "Area",
  area as "Category",
  status as "Status"
FROM "2-Areas"
WHERE contains(related, "<% tp.file.title %>") OR contains(file.content, "[[<% tp.file.title %>]]")
```

## Related Resources
```dataview
LIST
FROM "3-Resources"
WHERE contains(tags, "<% await tp.system.prompt("Main Tag", "guide") %>") AND file.name != "<% tp.file.title %>"
LIMIT 5
```

## Notes
<!-- Any additional notes -->

## Create Related Notes
- [[<% tp.file.title %> Project|Create Related Project]]
- [[<% tp.file.title %> Implementation|Create Implementation Guide]]
- [[<% tp.file.title %> Reference|Create Quick Reference]]

## Related
- [[3-Resources]]
- [[Resources TOC]]
- [[<% await tp.system.prompt("Related Resource TOC", "Guides TOC") %>]]
