---
creation_date: <% tp.date.now("YYYY-MM-DD") %>
modification_date: <% tp.date.now("YYYY-MM-DD") %>
type: person
first_name: <% await tp.system.prompt("First Name", "") %>
last_name: <% await tp.system.prompt("Last Name", "") %>
organization: <% await tp.system.prompt("Organization", "") %>
role: <% await tp.system.prompt("Role", "") %>
email: <% await tp.system.prompt("Email", "") %>
phone: <% await tp.system.prompt("Phone", "") %>
tags: [person, <% await tp.system.prompt("Additional Tags (comma-separated)", "contact") %>]
related: [<% await tp.system.prompt("Related Notes (comma-separated)", "") %>]
area: <% await tp.system.prompt("Related Area (if applicable)", "") %>
project: <% await tp.system.prompt("Related Project (if applicable)", "") %>
last_contact: <% tp.date.now("YYYY-MM-DD") %>
relationship: <% await tp.system.prompt("Relationship", "", "colleague, client, friend, family, service provider") %>
---

# <% tp.file.title %>

## Contact Information
- **Name**: <% await tp.system.prompt("First Name", "") %> <% await tp.system.prompt("Last Name", "") %>
- **Organization**: <% await tp.system.prompt("Organization", "") %>
- **Role**: <% await tp.system.prompt("Role", "") %>
- **Email**: <% await tp.system.prompt("Email", "") %>
- **Phone**: <% await tp.system.prompt("Phone", "") %>
- **Address**: <% await tp.system.prompt("Address", "") %>
- **Website**: <% await tp.system.prompt("Website", "") %>

## Background
<!-- Background information about this person -->

## Interactions
<!-- Log of interactions with this person -->
### <% tp.date.now("YYYY-MM-DD") %> - Initial Contact
-

## Notes
<!-- Any additional notes about this person -->
-

## Related Projects
```dataview
LIST
FROM "1-Projects"
WHERE contains(stakeholders, "<% tp.file.title %>") OR contains(file.content, "[[<% tp.file.title %>]]")
```

## Related Areas
```dataview
LIST
FROM "2-Areas"
WHERE contains(key_contacts, "<% tp.file.title %>") OR contains(file.content, "[[<% tp.file.title %>]]")
```

## Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  date as "Date",
  time as "Time",
  location as "Location"
FROM #meeting
WHERE contains(participants, "<% tp.file.title %>") OR contains(file.content, "[[<% tp.file.title %>]]")
SORT date DESC
```

## Create Related Notes
- [[Meeting with <% tp.file.title %> <% tp.date.now("YYYY-MM-DD") %>|Schedule Meeting]]
- [[<% tp.file.title %> Project|Create Project]]
- [[<% tp.file.title %> Task|Create Task]]

## Related
- [[People TOC]]
- [[<% await tp.system.prompt("Organization", "") %>]]
