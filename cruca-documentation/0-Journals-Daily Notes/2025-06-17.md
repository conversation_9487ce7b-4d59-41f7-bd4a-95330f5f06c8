---
creation_date: 2025-06-17 17:49
modification_date: Tuesday 17th June 2025 17:49:27
type: daily
date: 2025-06-17
day_of_week: Tuesday
week: 2025-W25
month: 2025-06
tags:
  - daily
  - 2025-06
  - cruca
mood: ""
energy_level: ""
weather: ""
location:
---

# 2025-06-17 - Tuesday

<< [[2025-06-16]] | [[2025-06-18]] >>

> **🔄 Template not processed?** Click here: [Process Template](obsidian://advanced-uri?vault=cruca-docs&commandid=templater-obsidian%3Areplace-in-file-templater)

## Quick Capture
<!-- Use this section for quick thoughts that you'll organize later -->
- Add hall hire price review with new market research for possible increase to hall hire costs.

## Tasks for Today
#todo #tasks #outstanding
### 📋 Carried Forward Tasks (92 tasks from 6 days)
#### 🔥 Recent Tasks (Last 7 Days)
##### From 2025-06-13 (4 days ago) - 46 tasks
- [ ] **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mailfrom post office
- [ ] Change the 2025 CRUC Calendar - Events by X spreadsheet. :
	- [ ] swap the pacific combined dinner from 9 aug to 16th of august to accomodate the new bunnings saus Wendy has mentioned will be swapping
- [ ] order new business cards for information update 
- [ ] add the upper caboolture craft section to cruca.org website
- [ ] timesheet to fill for CRCC meetings
- [ ] check Koorong
- [ ] Transfer the 100k on friday as there is 100k limit on bank acc
- [ ] Check license agreements are current once a year (annual checks. over christmas holiday period)
- [ ] check communion cup stocks every month
- [ ] Email Telstra and contact to update details to get invoices sent better method
- [ ] contacts? ********** from 11/01 & **********
- [ ] Need to write up payment vouchers for the payments made today:
	- [ ] ![[cruca-documentation/2-Areas/Finance Billing/May/Pasted image **************.png]]
- [ ] investigate why telstra bill not paid and out of date:
	- [ ] call/contact telstra to update email adress. Ask peter if he has. see emails confirming he has <NAME_EMAIL>
- [ ] ![[Selfie 2025-05-30 at 01.33.22 1.png]]
- [ ] #fayL:
	- [ ] **Garden Plants**
	- [ ] **Elders and Church Council Representatives**
	- [ ] **Are Your Skills Needed?**
- [ ] #jasonG:
	- [ ] Men’s Dinner
	- [ ] Elders & Pastoral Carers Gathering tuesday
- [ ] #fayM:
	- [ ] s
- [ ] #dawnG
	- [ ] Cafe church on friday 30th
- [ ] ask #Ian about ncps account details for online.
- [ ] received john wiers personal info sheet for membership. Filed in the admin folder in desk cabinet for filing next thursday #Directory
- [ ] Reflect on #kirsty recommendation to discuss and complete the church directory alongside the annual reports
- [ ] Investigate email errors with bulletin inserts
- [ ] Check graphics card issues on church computer
- [ ] Follow up with Ian about bulletin content
- [ ] Call Namecheap if quota exceeded error persists
- [ ] Arrange time to swap graphics card
- [ ] Confirm with Ian that bulletin inserts were received
- [ ] Prepare administrator's report for April church council meeting
- [ ] Follow up on hall hire inquiry from Catherine Hicks
- [ ] Update website with new service times
- [ ] Check progress on Safe Church audit requirements
- [ ] Respond to emails from church council members
- [ ] Call Unity Water about changing bill delivery method to email
- [ ] Follow up with Peter Mortimer about account authorization
- [ ] Contact Synod about web domain for cabreguca.com.au

#### 📚 Older Tasks (46 tasks from 5 days)
<details>
<summary>Click to expand older tasks</summary>

##### From 2025-05-29 (19 days ago) - 18 tasks
- [ ]  **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mailfrom post office
- [ ] Change the 2025 CRUC Calendar - Events by X spreadsheet. :
	- [ ] swap the pacific combined dinner from 9 aug to 16th of august to accomodate the new bunnings saus Wendy has mentioned will be swapping
- [ ] order new business cards for information update 
- [ ] add the upper caboolture craft section to cruca.org website
- [ ] timesheet to fill for CRCC meetings
- [ ] check Koorong
- [ ] Transfer the 100k on friday as there is 100k limit on bank acc
- [ ] Check license agreements are current once a year (annual checks. over christmas holiday period)
- [ ] check communion cup stocks every month
- [ ] Email Telstra and contact to update details to get invoices sent better method
- [ ] contacts? ********** from 11/01 & **********
- [ ] Need to write up payment vouchers for the payments made today:
	- [ ] ![[cruca-documentation/2-Areas/Finance Billing/May/Pasted image **************.png]]
- [ ] investigate why telstra bill not paid and out of date:
	- [ ] call/contact telstra to update email adress. Ask peter if he has. see emails confirming he has <NAME_EMAIL>
- [ ] ![[Selfie 2025-05-30 at 01.33.22 1.png]]

##### From 2025-05-16 (32 days ago) - 11 tasks
- [ ] #fayL:
	- [ ] **Garden Plants**
	- [ ] **Elders and Church Council Representatives**
	- [ ] **Are Your Skills Needed?**
- [ ] #jasonG:
	- [ ] Men’s Dinner
	- [ ] Elders & Pastoral Carers Gathering
- [ ] #fayM:
	- [ ] s
- [ ] #dawnG
	- [ ] Cafe church on friday 30th

##### From 2025-05-15 (33 days ago) - 3 tasks
- [ ] ask #Ian about ncps account details for online.
- [ ] received john wiers personal info sheet for membership. Filed in the admin folder in desk cabinet for filing next thursday #Directory
- [ ] Reflect on #kirsty recommendation to discuss and complete the church directory alongside the annual reports

##### From 2025-04-18 (60 days ago) - 6 tasks
- [ ] Investigate email errors with bulletin inserts
- [ ] Check graphics card issues on church computer
- [ ] Follow up with Ian about bulletin content
- [ ] Call Namecheap if quota exceeded error persists
- [ ] Arrange time to swap graphics card
- [ ] Confirm with Ian that bulletin inserts were received

##### From 2025-04-16 (62 days ago) - 8 tasks
- [ ] Prepare administrator's report for April church council meeting
- [ ] Follow up on hall hire inquiry from Catherine Hicks
- [ ] Update website with new service times
- [ ] Check progress on Safe Church audit requirements
- [ ] Respond to emails from church council members
- [ ] Call Unity Water about changing bill delivery method to email
- [ ] Follow up with Peter Mortimer about account authorization
- [ ] Contact Synod about web domain for cabreguca.com.au

</details>

### ✨ New Tasks for Today

- [ ] 
- [ ] 
- [ ] 

## Church Matters
<!-- Church-related activities, meetings, pastoral care, services -->
- Admin Report:


## Hall Hire
<!-- Hall bookings, inquiries, maintenance, key management -->
- 

## Administration
<!-- Administrative tasks, correspondence, documentation, reports -->
 ### #admin/report 
 - add safe church progress and protecht tasks to present to council
 - #accounts #bills #critical/services #admin/report Must request minute for Telstra account amendments. urgent as the account could be going overdue.
	 - Jordan Pacey to have access to telstra account for the Uniting Church in Australia Telstra Business account, with **account number**: `**********` **named**: Uniting Church in Australia
- Minute for authorisation of the following property services the Moreton Bay regional Council, unity water and AGL remove all previous contacts
## Finance & Banking
<!-- Banking, donations, payments, reconciliation, invoices -->
- 

## Safe Church
<!-- Compliance, training, documentation, Blue Card updates -->
#safe-church 
-   Safe church trainsing cday to watch resources.
	- at the end pause on the quiz ato answer
	- Safe church training courses 
		- https://vimeo.com/showcase/1137726
-  #admin/report appoint a date for training 
	- #presbytery support for Moreton rivers is meri stoerr. Possible could get more support/training from her
	- alternatively i can arrange a day to deliver the training using the resources froim the synod hub webpage
- #admin/report all of our volunteers (not just volunteers in children ministry) must sign. astatement of personal commitment
	- For insurance its expected all volunteers sign this. for the church, we support this because we believe all of our volunteers are equally important
		- if there is alot of q's/pushback -> organise safe church to come out for QA Safe church or presbytry suppport or both
- #admin/report safe church audit is a big undertaking, and safe church is bvery keen to support congregations and want to share that burden. There to assist. Don't expect erverything to chnaged and updated overnight, hjust looking at wahasty the next stesops are and whats more helpful to the vcongregartion
	- safe church audit is a big undertaking, and safe church is bvery keen to support congregations and want to share that burden. There to assist. Don't expect erverything to chnaged and updated overnight, hjust looking at wahasty the next stesops are and whats more helpful to the vcongregartion


## Communication
<!-- Emails, phone calls, website updates, social media -->
- 

## Facilities & Maintenance
<!-- Building maintenance, equipment, cleaning, security -->
- 

## Follow-ups
<!-- Items requiring follow-up action with specific people/organizations -->
- [ ] 
- [ ] 

## Journal
<!-- How was your day? What happened? What did you learn? -->
- 

## Notes
<!-- Any other notes or information -->
- 

## Active Projects
```dataview
TABLE WITHOUT ID
  file.link as "Project",
  priority as "Priority",
  deadline as "Deadline",
  completion_percentage + "%" as "Progress"
FROM "1-Projects"
WHERE status = "active" OR !contains(status, "completed")
SORT priority ASC, deadline ASC
LIMIT 5
```

## Today's Meetings
```dataview
TABLE WITHOUT ID
  file.link as "Meeting",
  time as "Time",
  participants as "Participants",
  location as "Location"
FROM #meeting
WHERE date = date(2025-06-17)
SORT time ASC
```

## Today's Tasks Overview
```tasks
not done
due on 2025-06-17
```

## Tasks from This Note (All)
```dataview
TASK
FROM "cruca-documentation/0-Journals-Daily Notes/2025-06-17.md"
WHERE !completed
```

## Tasks from This Note (With Due Dates)
```dataview
TASK
FROM "cruca-documentation/0-Journals-Daily Notes/2025-06-17.md"
WHERE !completed AND due
SORT due ASC
```

## This Week's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-15) AND due <= date(2025-06-21)
SORT due ASC
```

## This Week's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-06-14
due before 2025-06-22
```

## This Month's Tasks (Dataview)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-06-01) AND due <= date(2025-06-31)
SORT due ASC
```

## This Month's Tasks (Tasks Plugin)
```tasks
not done
due after 2025-06-01
due before 2025-07-01
```

## This Quarter's Tasks

```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due >= date(2025-04-01) AND due <= date(2025-06-30)
SORT due ASC
```

## Overdue Tasks
```tasks
not done
due before 2025-06-17
```

## Upcoming Tasks (Next 7 Days)
```tasks
not done
due after 2025-06-17
due before 2025-06-24
```

## All Incomplete Tasks (No Due Date Required)
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed
SORT file.name ASC
LIMIT 20
```

## Church Administration Tasks (With Due Dates)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND due AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT due ASC
LIMIT 10
```

## Church Administration Tasks (All)
```dataview
TASK
FROM "2-Areas" OR "1-Projects"
WHERE !completed AND (contains(tags, "church") OR contains(tags, "admin") OR contains(tags, "hall-hire") OR contains(tags, "finance"))
SORT file.name ASC
LIMIT 15
```

## Recent Notes
```dataview
TABLE WITHOUT ID
  file.link as "Note",
  type as "Type",
  file.mtime as "Last Modified"
FROM -"Daily" AND -"Templates"
SORT file.mtime DESC
LIMIT 5
```

## Today's Events & Reminders
```dataview
TABLE WITHOUT ID
  file.link as "Event",
  time as "Time",
  location as "Location",
  description as "Description"
FROM "Events&Meeting Dates" OR "2-Areas/0-Monthly-Tasks&Reports"
WHERE date = date(2025-06-17)
SORT time ASC
```

## Weekly Recurring Events (Today: Tuesday)
```dataview
TABLE WITHOUT ID
  file.link as "Activity",
  time as "Time",
  location as "Location",
  description as "Notes"
FROM "Events&Meeting Dates"
WHERE contains(tags, "recurring") AND contains(file.content, "Tuesday")
```

## Create New Note
- [[2025-06-17 Meeting|Create New Meeting]]
- [[2025-06-17 Task|Create New Task]]
- [[2025-06-17 Hall Hire|Create Hall Hire Booking]]
- [[2025-06-17 Finance|Create Finance Note]]

## Related
- [[Daily Notes TOC]]
- [[2025-06|Monthly Overview]]
- [[2025-W25|Weekly Overview]]
- [[Comprehensive Administrative Procedures Guide]]
- [[Home]]
