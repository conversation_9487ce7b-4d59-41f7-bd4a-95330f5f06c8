---
creation_date: 2025-04-16
modification_date: 2025-04-16
type: resource
tags: [para/resources, tasks, dataview, formatting, guide]
related: [Templates, Daily Notes TOC]
---

# Task Format Guide - Dataview vs Tasks Plugin

## The Issue: Why Your Tasks Aren't Showing

Your tasks aren't appearing in the dataview tables because **most of your tasks don't have due dates** in the format that Dataview expects. Let me explain the difference:

### 🔍 **Your Current Tasks (From 2025-05-29.md):**
```markdown
- [ ] **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mail
- [ ] Change the 2025 CRUC Calendar - Events by X spreadsheet
- [ ] order new business cards for information update
- [ ] Bank Craft Stall Money
```

**Problem**: These tasks have **no formal due dates** that Dataview can recognize.

## Task Format Requirements

### 📅 **For Dataview TASK Queries:**
Dataview needs tasks with **formal due dates** in these formats:

#### ✅ **Correct Formats:**
```markdown
- [ ] Task with due date 📅 2025-05-30
- [ ] Task with due date [due:: 2025-05-30]
- [ ] Task with due date (due: 2025-05-30)
- [ ] Task 📆 2025-05-30
```

#### ❌ **Won't Work for Dataview:**
```markdown
- [ ] Task needs to be done 30/05 friday
- [ ] Task for next week
- [ ] Task (no date at all)
```

### 🔧 **For Tasks Plugin Queries:**
The Tasks plugin is more flexible and can work with:
- Tasks with formal due dates
- Tasks with natural language dates
- Tasks without dates (when using broader queries)

## Solutions

### 🎯 **Solution 1: Add Formal Due Dates (Recommended)**

Update your tasks to include formal due dates:

#### Before:
```markdown
- [ ] **Directory** needs to be worked on 30/05 friday
- [ ] **collect mail** Need to pay invoices sent via mail
- [ ] Bank Craft Stall Money
```

#### After:
```markdown
- [ ] **Directory** needs to be worked on 📅 2025-05-30
- [ ] **collect mail** Need to pay invoices sent via mail 📅 2025-05-31
- [ ] Bank Craft Stall Money 📅 2025-06-01
```

### 🔧 **Solution 2: Use Mixed Queries (What I Added)**

I've updated your templates to include both types:

#### For Tasks WITH Due Dates:
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed AND due
SORT due ASC
```

#### For ALL Tasks (No Due Date Required):
```dataview
TASK
FROM "2-Areas" OR "1-Projects" OR "0-Journals-Daily Notes"
WHERE !completed
SORT file.name ASC
```

### 🚀 **Solution 3: Use Tasks Plugin Queries**

I've also added Tasks plugin queries that are more flexible:

```tasks
not done
path includes 2-Areas
```

## Updated Template Sections

I've added these new sections to your templates:

### 📋 **New Task Views:**
1. **Tasks from This Note (All)** - Shows all tasks regardless of due dates
2. **Tasks from This Note (With Due Dates)** - Only tasks with formal due dates
3. **This Week's Tasks (Dataview)** - Dataview version requiring due dates
4. **This Week's Tasks (Tasks Plugin)** - Tasks plugin version (more flexible)
5. **All Incomplete Tasks** - Shows all incomplete tasks from your vault
6. **Church Administration Tasks (All)** - All church tasks regardless of due dates

## How to Add Due Dates

### 📅 **Method 1: Emoji Format (Easiest)**
```markdown
- [ ] Task description 📅 2025-05-30
- [ ] Another task 📆 2025-06-01
```

### 📝 **Method 2: Dataview Property**
```markdown
- [ ] Task description [due:: 2025-05-30]
- [ ] Another task [due:: 2025-06-01]
```

### ⏰ **Method 3: Tasks Plugin Format**
```markdown
- [ ] Task description 📅 2025-05-30
- [ ] Task with time 📅 2025-05-30 ⏰ 14:00
- [ ] Recurring task 📅 2025-05-30 🔁 every week
```

## Quick Fix for Your Current Tasks

Here's how to quickly add due dates to your existing tasks:

### 🔄 **Your Tasks Updated:**
```markdown
## Tasks for Today
### ✨ Tasks for Today
- [ ] **Directory** needs to be worked on 📅 2025-05-30
- [ ] **collect mail** Need to pay invoices sent via mail 📅 2025-05-31
- [ ] Change the 2025 CRUC Calendar - Events by X spreadsheet 📅 2025-06-01
- [ ] order new business cards for information update 📅 2025-06-05
- [ ] add the upper caboolture craft section to cruca.org website 📅 2025-06-07
- [ ] Bank Craft Stall Money 📅 2025-05-31
- [ ] timesheet to fill for CRCC meetings 📅 2025-05-30
- [ ] check Koorong 📅 2025-06-01
- [ ] Transfer the 100k on friday 📅 2025-05-31

**Hall Hire:**
- [ ] Check license agreements are current 📅 2025-12-31
- [ ] check communion cup stocks every month 📅 2025-06-01

**Finance:**
- [ ] Need to write up payment vouchers 📅 2025-05-30
- [ ] investigate why telstra bill not paid 📅 2025-05-31
```

## Testing Your Setup

### 🧪 **Test Steps:**
1. **Add a due date** to one of your tasks using `📅 2025-05-30`
2. **Create a new daily note** using your template
3. **Check the dataview sections** - you should now see the task with due date
4. **Check the "All Tasks" sections** - you should see all tasks

### 🔍 **Troubleshooting:**
If tasks still don't show:
1. **Check file paths** in dataview queries match your vault structure
2. **Ensure tasks are properly formatted** with `- [ ]` syntax
3. **Try the "All Incomplete Tasks" section** first (no due dates required)
4. **Click the "Process Template" link** to ensure template is processed

## Recommended Workflow

### 🌟 **Best Practice:**
1. **Use emoji due dates** (`📅 2025-05-30`) for simplicity
2. **Add due dates** to important tasks
3. **Use "All Tasks" views** for tasks without specific deadlines
4. **Use Tasks plugin queries** for more flexible searching

### ⚙️ **Your Tasks Plugin Settings:**
Your configuration is correct:
- ✅ `taskFormat: "dataview"` - Good for compatibility
- ✅ `setDoneDate: true` - Tracks completion dates
- ✅ `autoSuggestInEditor: true` - Helps with task creation

## Related Resources
- [[Enhanced Church Daily Template]] - Updated template with mixed task views
- [[Templater Auto-Processing Setup Guide]] - Template processing help
- [Dataview Task Documentation](https://blacksmithgu.github.io/obsidian-dataview/queries/query-types/#task-queries)
- [Tasks Plugin Documentation](https://obsidian-tasks-group.github.io/obsidian-tasks/)
